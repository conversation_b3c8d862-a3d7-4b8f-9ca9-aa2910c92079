# نظام Oracle المتكامل

نظام إدارة متكامل باستخدام قاعدة بيانات Oracle مع واجهة رسومية بـ Python.

## المميزات

- **إدارة المستخدمين**: إنشاء وتعديل وحذف المستخدمين مع مستويات صلاحيات مختلفة
- **إدارة المنتجات**: إدارة كاملة للمنتجات والفئات مع تتبع المخزون
- **إدارة الطلبات**: نظام طلبات متكامل مع تتبع الحالة
- **التقارير**: تقارير شاملة عن المبيعات والمخزون
- **واجهة رسومية**: واجهة سهلة الاستخدام باللغة العربية

## متطلبات النظام

### قاعدة البيانات
- Oracle Database 11g أو أحدث
- Oracle Instant Client
- مستخدم قاعدة بيانات بصلاحيات إنشاء الجداول

### Python
- Python 3.7 أو أحدث
- المكتبات المطلوبة (انظر requirements.txt)

## التثبيت

### 1. تثبيت Oracle Instant Client

قم بتحميل وتثبيت Oracle Instant Client من:
https://www.oracle.com/database/technologies/instant-client.html

### 2. تثبيت Python Dependencies

```bash
pip install -r requirements.txt
```

### 3. إعداد قاعدة البيانات

1. قم بتشغيل ملفات SQL بالترتيب التالي:
   ```sql
   -- إنشاء الجداول
   @oracle_system/database/create_tables.sql
   
   -- إنشاء الفهارس
   @oracle_system/database/create_indexes.sql
   
   -- إنشاء الإجراءات المخزنة
   @oracle_system/database/stored_procedures.sql
   
   -- إدراج البيانات التجريبية (اختياري)
   @oracle_system/database/sample_data.sql
   ```

### 4. تكوين الاتصال

قم بتعديل إعدادات الاتصال في:
`oracle_system/config/database_config.py`

أو استخدم متغيرات البيئة:
```bash
set ORACLE_HOST=localhost
set ORACLE_PORT=1521
set ORACLE_SERVICE=XEPDB1
set ORACLE_USER=your_username
set ORACLE_PASSWORD=your_password
```

## التشغيل

```bash
cd oracle_system
python main.py
```

## بيانات تسجيل الدخول التجريبية

| اسم المستخدم | كلمة المرور | النوع |
|---------------|-------------|-------|
| admin | admin123 | مدير النظام |
| manager1 | manager123 | مدير |
| user1 | user123 | مستخدم |
| user2 | user123 | مستخدم |

## هيكل المشروع

```
oracle_system/
├── main.py                 # الملف الرئيسي
├── config/                 # إعدادات النظام
│   ├── database_config.py  # إعدادات قاعدة البيانات
│   └── database_connection.py # إدارة الاتصال
├── models/                 # نماذج البيانات
│   ├── user.py            # نموذج المستخدم
│   ├── product.py         # نموذج المنتج
│   ├── category.py        # نموذج الفئة
│   ├── order.py           # نموذج الطلب
│   └── order_detail.py    # نموذج تفاصيل الطلب
├── dao/                   # طبقة الوصول للبيانات
│   ├── base_dao.py        # الفئة الأساسية
│   ├── user_dao.py        # عمليات المستخدمين
│   ├── product_dao.py     # عمليات المنتجات
│   ├── category_dao.py    # عمليات الفئات
│   └── order_dao.py       # عمليات الطلبات
├── gui/                   # واجهة المستخدم
│   ├── login_window.py    # نافذة تسجيل الدخول
│   └── simple_main.py     # النافذة الرئيسية
└── database/              # ملفات قاعدة البيانات
    ├── create_tables.sql  # إنشاء الجداول
    ├── create_indexes.sql # إنشاء الفهارس
    ├── stored_procedures.sql # الإجراءات المخزنة
    └── sample_data.sql    # البيانات التجريبية
```

## الاستخدام

### تسجيل الدخول
1. قم بتشغيل التطبيق
2. أدخل اسم المستخدم وكلمة المرور
3. اضغط "تسجيل الدخول"

### إدارة البيانات
- **المستخدمين**: إضافة وتعديل وحذف المستخدمين (للمديرين فقط)
- **الفئات**: إدارة فئات المنتجات
- **المنتجات**: إضافة وتعديل المنتجات مع تتبع المخزون
- **الطلبات**: إنشاء ومتابعة الطلبات

### التقارير
- تقرير المنتجات
- تقرير الطلبات
- تقرير المنتجات منخفضة المخزون

## استكشاف الأخطاء

### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل Oracle Database
2. تحقق من صحة بيانات الاتصال
3. تأكد من تثبيت Oracle Instant Client

### خطأ في تسجيل الدخول
1. تحقق من صحة اسم المستخدم وكلمة المرور
2. تأكد من وجود البيانات التجريبية في قاعدة البيانات

### خطأ في تحميل البيانات
1. تأكد من إنشاء جميع الجداول
2. تحقق من صلاحيات المستخدم في قاعدة البيانات

## التطوير

لإضافة مميزات جديدة:
1. أنشئ النموذج في مجلد `models/`
2. أنشئ فئة DAO في مجلد `dao/`
3. أضف واجهة المستخدم في مجلد `gui/`
4. حدث النافذة الرئيسية

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء issue في المستودع.

# -*- coding: utf-8 -*-
"""
Database Connection Manager
مدير الاتصال بقاعدة البيانات
"""

try:
    import cx_Oracle
except ImportError:
    print("تحذير: مكتبة cx_Oracle غير مثبتة. يرجى تثبيتها باستخدام: pip install cx_Oracle")
    cx_Oracle = None

from .database_config import db_config

class DatabaseConnection:
    """مدير الاتصال بقاعدة البيانات"""
    
    def __init__(self):
        self.connection = None
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        if cx_Oracle is None:
            raise Exception("مكتبة cx_Oracle غير متوفرة")
        
        try:
            dsn = db_config.get_dsn()
            self.connection = cx_Oracle.connect(
                user=db_config.username,
                password=db_config.password,
                dsn=dsn
            )
            return True
        except Exception as e:
            print(f"خطأ في الاتصال: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام"""
        if not self.connection:
            if not self.connect():
                raise Exception("فشل في الاتصال بقاعدة البيانات")
        
        cursor = self.connection.cursor()
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                self.connection.commit()
                return cursor.rowcount
        finally:
            cursor.close()

# مثيل عام من مدير الاتصال
db_connection = DatabaseConnection()

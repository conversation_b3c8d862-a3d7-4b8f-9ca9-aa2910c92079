# -*- coding: utf-8 -*-
"""
Simple Main Window
نافذة رئيسية مبسطة
"""

import tkinter as tk
from tkinter import ttk, messagebox
from ..models.user import User

class MainWindow:
    """النافذة الرئيسية المبسطة"""
    
    def __init__(self, user: User):
        self.current_user = user
        
        # إنشاء النافذة الرئيسية
        self.root = tk.Tk()
        self.root.title(f"نظام Oracle - {user.full_name}")
        self.root.geometry("800x600")
        
        # إنشاء واجهة المستخدم
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # عنوان رئيسي
        title_label = ttk.Label(self.root, text="نظام إدارة Oracle", 
                               font=("Arial", 20, "bold"))
        title_label.pack(pady=20)
        
        # معلومات المستخدم
        user_frame = ttk.LabelFrame(self.root, text="معلومات المستخدم", padding="10")
        user_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Label(user_frame, text=f"الاسم: {self.current_user.full_name}").pack(anchor=tk.W)
        ttk.Label(user_frame, text=f"اسم المستخدم: {self.current_user.username}").pack(anchor=tk.W)
        ttk.Label(user_frame, text=f"النوع: {self.current_user.user_type}").pack(anchor=tk.W)
        ttk.Label(user_frame, text=f"البريد: {self.current_user.email}").pack(anchor=tk.W)
        
        # الوظائف المتاحة
        functions_frame = ttk.LabelFrame(self.root, text="الوظائف المتاحة", padding="10")
        functions_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # أزرار الوظائف
        if self.current_user.can_manage_users():
            ttk.Button(functions_frame, text="إدارة المستخدمين", 
                      command=self.manage_users).pack(pady=5, fill=tk.X)
        
        if self.current_user.can_manage_products():
            ttk.Button(functions_frame, text="إدارة المنتجات", 
                      command=self.manage_products).pack(pady=5, fill=tk.X)
            ttk.Button(functions_frame, text="إدارة الفئات", 
                      command=self.manage_categories).pack(pady=5, fill=tk.X)
        
        ttk.Button(functions_frame, text="إدارة الطلبات", 
                  command=self.manage_orders).pack(pady=5, fill=tk.X)
        
        ttk.Button(functions_frame, text="التقارير", 
                  command=self.show_reports).pack(pady=5, fill=tk.X)
        
        # أزرار التحكم
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=20, pady=10)
        
        ttk.Button(control_frame, text="تسجيل خروج", 
                  command=self.logout).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="خروج", 
                  command=self.exit_app).pack(side=tk.LEFT, padx=5)
        
        # شريط الحالة
        self.status_var = tk.StringVar()
        self.status_var.set("مرحباً بك في نظام Oracle")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def manage_users(self):
        """إدارة المستخدمين"""
        messagebox.showinfo("إدارة المستخدمين", "وظيفة إدارة المستخدمين قيد التطوير")
    
    def manage_products(self):
        """إدارة المنتجات"""
        messagebox.showinfo("إدارة المنتجات", "وظيفة إدارة المنتجات قيد التطوير")
    
    def manage_categories(self):
        """إدارة الفئات"""
        messagebox.showinfo("إدارة الفئات", "وظيفة إدارة الفئات قيد التطوير")
    
    def manage_orders(self):
        """إدارة الطلبات"""
        messagebox.showinfo("إدارة الطلبات", "وظيفة إدارة الطلبات قيد التطوير")
    
    def show_reports(self):
        """عرض التقارير"""
        messagebox.showinfo("التقارير", "وظيفة التقارير قيد التطوير")
    
    def logout(self):
        """تسجيل خروج"""
        if messagebox.askokcancel("تسجيل خروج", "هل تريد تسجيل الخروج؟"):
            self.root.destroy()
    
    def exit_app(self):
        """خروج من التطبيق"""
        if messagebox.askokcancel("خروج", "هل تريد الخروج من النظام؟"):
            self.root.quit()
    
    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

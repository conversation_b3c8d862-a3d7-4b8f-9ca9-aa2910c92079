-- Oracle Database Sample Data
-- بيانات تجريبية للنظام

-- إدراج فئات المنتجات
INSERT INTO CATEGORIES (CATEGORY_ID, CATEGORY_NAME, DESCRIPTION) VALUES 
(CATEGORY_SEQ.NEXTVAL, 'إلكترونيات', 'أجهزة إلكترونية ومعدات تقنية');

INSERT INTO CATEGORIES (CATEGORY_ID, CATEGORY_NAME, DESCRIPTION) VALUES 
(CATEGORY_SEQ.NEXTVAL, 'ملابس', 'ملابس رجالية ونسائية وأطفال');

INSERT INTO CATEGORIES (CATEGORY_ID, CATEGORY_NAME, DESCRIPTION) VALUES 
(CATEGORY_SEQ.NEXTVAL, 'كتب', 'كتب تعليمية وثقافية وأدبية');

-- إدراج مستخدمين تجريبيين (كلمات المرور مشفرة بـ SHA256)
INSERT INTO USERS (USER_ID, USERNAME, PASSWORD, EMAIL, FULL_NAME, PHONE, USER_TYPE) VALUES 
(USER_SEQ.NEXTVAL, 'admin', '240be518fabd2724ddb6f04eeb1da5967448d7e831c08c8fa822809f74c720a9', '<EMAIL>', 'مدير النظام', '0501234567', 'ADMIN');

INSERT INTO USERS (USER_ID, USERNAME, PASSWORD, EMAIL, FULL_NAME, PHONE, USER_TYPE) VALUES 
(USER_SEQ.NEXTVAL, 'manager1', 'ef92b778bafe771e89245b89ecbc08a44a4e166c06659911881f383d4473e94f', '<EMAIL>', 'أحمد محمد', '0507654321', 'MANAGER');

INSERT INTO USERS (USER_ID, USERNAME, PASSWORD, EMAIL, FULL_NAME, PHONE, USER_TYPE) VALUES 
(USER_SEQ.NEXTVAL, 'user1', '0a041b9462caa4a31bac3567e0b6e6fd9100787db2ab433d96f6d178cabfce90', '<EMAIL>', 'فاطمة علي', '0509876543', 'USER');

-- إدراج منتجات تجريبية
INSERT INTO PRODUCTS (PRODUCT_ID, PRODUCT_NAME, DESCRIPTION, CATEGORY_ID, PRICE, QUANTITY_IN_STOCK, MIN_STOCK_LEVEL) VALUES 
(PRODUCT_SEQ.NEXTVAL, 'لابتوب Dell Inspiron', 'لابتوب Dell Inspiron 15 بمعالج Intel Core i5', 1, 2500.00, 10, 3);

INSERT INTO PRODUCTS (PRODUCT_ID, PRODUCT_NAME, DESCRIPTION, CATEGORY_ID, PRICE, QUANTITY_IN_STOCK, MIN_STOCK_LEVEL) VALUES 
(PRODUCT_SEQ.NEXTVAL, 'هاتف Samsung Galaxy', 'هاتف ذكي Samsung Galaxy A54', 1, 1200.00, 15, 5);

INSERT INTO PRODUCTS (PRODUCT_ID, PRODUCT_NAME, DESCRIPTION, CATEGORY_ID, PRICE, QUANTITY_IN_STOCK, MIN_STOCK_LEVEL) VALUES 
(PRODUCT_SEQ.NEXTVAL, 'قميص قطني', 'قميص قطني عالي الجودة', 2, 85.00, 25, 10);

INSERT INTO PRODUCTS (PRODUCT_ID, PRODUCT_NAME, DESCRIPTION, CATEGORY_ID, PRICE, QUANTITY_IN_STOCK, MIN_STOCK_LEVEL) VALUES 
(PRODUCT_SEQ.NEXTVAL, 'كتاب البرمجة', 'كتاب تعلم البرمجة للمبتدئين', 3, 45.00, 30, 5);

COMMIT;

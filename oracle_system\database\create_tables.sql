-- Oracle Database Schema Creation Script
-- نظام إدارة متكامل - إنشاء الجداول الأساسية

-- إن<PERSON>اء جدول المستخدمين
CREATE TABLE USERS (
    USER_ID NUMBER(10) PRIMARY KEY,
    USERNAME VARCHAR2(50) UNIQUE NOT NULL,
    PASSWORD VARCHAR2(100) NOT NULL,
    EMAIL VARCHAR2(100) UNIQUE NOT NULL,
    FULL_NAME VARCHAR2(100) NOT NULL,
    PHONE VARCHAR2(20),
    USER_TYPE VARCHAR2(20) DEFAULT 'USER' CHECK (USER_TYPE IN ('ADMIN', 'USER', 'MANAGER')),
    STATUS VARCHAR2(10) DEFAULT 'ACTIVE' CHECK (STATUS IN ('ACTIVE', 'INACTIVE')),
    CREATED_DATE DATE DEFAULT SYSDATE,
    LAST_LOGIN DATE
);

-- إنشاء تسلسل للمستخدمين
CREATE SEQUENCE USER_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;

-- إنشاء جدول الفئات
CREATE TABLE CATEGORIES (
    CATEGORY_ID NUMBER(10) PRIMARY KEY,
    CATEGORY_NAME VARCHAR2(100) NOT NULL,
    DESCRIPTION VARCHAR2(500),
    STATUS VARCHAR2(10) DEFAULT 'ACTIVE' CHECK (STATUS IN ('ACTIVE', 'INACTIVE')),
    CREATED_DATE DATE DEFAULT SYSDATE
);

-- إنشاء تسلسل للفئات
CREATE SEQUENCE CATEGORY_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;

-- إنشاء جدول المنتجات
CREATE TABLE PRODUCTS (
    PRODUCT_ID NUMBER(10) PRIMARY KEY,
    PRODUCT_NAME VARCHAR2(200) NOT NULL,
    DESCRIPTION VARCHAR2(1000),
    CATEGORY_ID NUMBER(10),
    PRICE NUMBER(10,2) NOT NULL,
    QUANTITY_IN_STOCK NUMBER(10) DEFAULT 0,
    MIN_STOCK_LEVEL NUMBER(10) DEFAULT 5,
    STATUS VARCHAR2(10) DEFAULT 'ACTIVE' CHECK (STATUS IN ('ACTIVE', 'INACTIVE', 'DISCONTINUED')),
    CREATED_DATE DATE DEFAULT SYSDATE,
    UPDATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_PRODUCT_CATEGORY FOREIGN KEY (CATEGORY_ID) REFERENCES CATEGORIES(CATEGORY_ID)
);

-- إنشاء تسلسل للمنتجات
CREATE SEQUENCE PRODUCT_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;

-- إنشاء جدول الطلبات
CREATE TABLE ORDERS (
    ORDER_ID NUMBER(10) PRIMARY KEY,
    USER_ID NUMBER(10) NOT NULL,
    ORDER_DATE DATE DEFAULT SYSDATE,
    TOTAL_AMOUNT NUMBER(12,2) NOT NULL,
    STATUS VARCHAR2(20) DEFAULT 'PENDING' CHECK (STATUS IN ('PENDING', 'CONFIRMED', 'SHIPPED', 'DELIVERED', 'CANCELLED')),
    SHIPPING_ADDRESS VARCHAR2(500),
    NOTES VARCHAR2(1000),
    CREATED_DATE DATE DEFAULT SYSDATE,
    CONSTRAINT FK_ORDER_USER FOREIGN KEY (USER_ID) REFERENCES USERS(USER_ID)
);

-- إنشاء تسلسل للطلبات
CREATE SEQUENCE ORDER_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;

-- إنشاء جدول تفاصيل الطلبات
CREATE TABLE ORDER_DETAILS (
    ORDER_DETAIL_ID NUMBER(10) PRIMARY KEY,
    ORDER_ID NUMBER(10) NOT NULL,
    PRODUCT_ID NUMBER(10) NOT NULL,
    QUANTITY NUMBER(10) NOT NULL,
    UNIT_PRICE NUMBER(10,2) NOT NULL,
    TOTAL_PRICE NUMBER(12,2) NOT NULL,
    CONSTRAINT FK_ORDER_DETAIL_ORDER FOREIGN KEY (ORDER_ID) REFERENCES ORDERS(ORDER_ID),
    CONSTRAINT FK_ORDER_DETAIL_PRODUCT FOREIGN KEY (PRODUCT_ID) REFERENCES PRODUCTS(PRODUCT_ID)
);

-- إنشاء تسلسل لتفاصيل الطلبات
CREATE SEQUENCE ORDER_DETAIL_SEQ START WITH 1 INCREMENT BY 1 NOCACHE;

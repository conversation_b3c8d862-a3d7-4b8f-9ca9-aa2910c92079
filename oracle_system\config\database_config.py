# -*- coding: utf-8 -*-
"""
Database Configuration
إعدادات قاعدة البيانات
"""

import os

class DatabaseConfig:
    """إعدادات قاعدة البيانات"""
    
    def __init__(self):
        self.host = os.getenv('ORACLE_HOST', 'localhost')
        self.port = int(os.getenv('ORACLE_PORT', 1521))
        self.service_name = os.getenv('ORACLE_SERVICE', 'XEPDB1')
        self.username = os.getenv('ORACLE_USER', 'hr')
        self.password = os.getenv('ORACLE_PASSWORD', 'hr')
    
    def get_dsn(self):
        """الحصول على DSN للاتصال"""
        return f"{self.host}:{self.port}/{self.service_name}"

# مثيل عام من الإعدادات
db_config = DatabaseConfig()

# -*- coding: utf-8 -*-
"""
Oracle System Main Application
التطبيق الرئيسي لنظام Oracle
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from gui.login_window import LoginWindow
from gui.main_window import MainWindow
from models.user import User

class OracleSystemApp:
    """التطبيق الرئيسي لنظام Oracle"""
    
    def __init__(self):
        self.current_user = None
        self.main_window = None
        self.login_window = None
    
    def start(self):
        """بدء التطبيق"""
        try:
            # إنشاء نافذة تسجيل الدخول
            self.login_window = LoginWindow(self.on_login_success)
            self.login_window.run()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في بدء التطبيق: {str(e)}")
    
    def on_login_success(self, user: User):
        """استدعاء عند نجاح تسجيل الدخول"""
        self.current_user = user
        
        # إنشاء النافذة الرئيسية
        self.main_window = MainWindow(user)
        
        # ربط إغلاق النافذة الرئيسية بالخروج
        self.main_window.root.protocol("WM_DELETE_WINDOW", self.on_main_window_close)
        
        # تشغيل النافذة الرئيسية
        self.main_window.run()
    
    def on_main_window_close(self):
        """استدعاء عند إغلاق النافذة الرئيسية"""
        if messagebox.askokcancel("تأكيد الخروج", "هل تريد الخروج من النظام؟"):
            self.main_window.root.destroy()
            if self.login_window:
                self.login_window.root.quit()

def main():
    """الدالة الرئيسية"""
    app = OracleSystemApp()
    app.start()

if __name__ == "__main__":
    main()

# -*- coding: utf-8 -*-
"""
Login Window
نافذة تسجيل الدخول
"""

import tkinter as tk
from tkinter import ttk, messagebox
from ..dao.user_dao import UserDAO

class LoginWindow:
    """نافذة تسجيل الدخول"""
    
    def __init__(self, on_login_success):
        self.on_login_success = on_login_success
        self.user_dao = UserDAO()
        
        # إنشاء النافذة
        self.root = tk.Tk()
        self.root.title("نظام Oracle - تسجيل الدخول")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إنشاء واجهة المستخدم
        self.create_widgets()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """إنشاء عناصر واجهة المستخدم"""
        # إطار رئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # عنوان
        title_label = ttk.Label(main_frame, text="نظام إدارة Oracle", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # اسم المستخدم
        ttk.Label(main_frame, text="اسم المستخدم:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar()
        self.username_entry = ttk.Entry(main_frame, textvariable=self.username_var, width=25)
        self.username_entry.grid(row=1, column=1, pady=5, padx=(10, 0))
        self.username_entry.focus()
        
        # كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        self.password_entry = ttk.Entry(main_frame, textvariable=self.password_var, 
                                       show="*", width=25)
        self.password_entry.grid(row=2, column=1, pady=5, padx=(10, 0))
        
        # أزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="تسجيل الدخول", 
                  command=self.login).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="إلغاء", 
                  command=self.cancel).pack(side=tk.LEFT, padx=5)
        
        # معلومات تجريبية
        info_frame = ttk.LabelFrame(main_frame, text="بيانات تجريبية", padding="10")
        info_frame.grid(row=4, column=0, columnspan=2, pady=20, sticky=(tk.W, tk.E))
        
        info_text = "المدير: admin / admin123\nالمستخدم: user1 / user123"
        ttk.Label(info_frame, text=info_text, font=("Arial", 9)).pack()
        
        # ربط مفتاح Enter
        self.root.bind('<Return>', lambda event: self.login())
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            user = self.user_dao.authenticate(username, password)
            
            if user:
                self.root.withdraw()  # إخفاء نافذة تسجيل الدخول
                self.on_login_success(user)
            else:
                messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
                self.password_var.set("")
                self.password_entry.focus()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تسجيل الدخول: {str(e)}")
    
    def cancel(self):
        """إلغاء تسجيل الدخول"""
        self.root.quit()
    
    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

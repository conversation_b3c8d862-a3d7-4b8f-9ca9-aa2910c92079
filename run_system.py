#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Oracle System Launcher
مشغل نظام Oracle
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from oracle_system.main import main
    
    if __name__ == "__main__":
        print("بدء تشغيل نظام Oracle...")
        print("Oracle System Starting...")
        main()
        
except ImportError as e:
    print(f"خطأ في استيراد الوحدات: {e}")
    print("يرجى التأكد من تثبيت جميع المتطلبات")
    print("pip install -r requirements.txt")
    
except Exception as e:
    print(f"خطأ في تشغيل النظام: {e}")
    input("اضغط Enter للخروج...")

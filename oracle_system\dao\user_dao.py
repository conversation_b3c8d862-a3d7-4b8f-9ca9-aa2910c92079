# -*- coding: utf-8 -*-
"""
User DAO
طبقة الوصول لبيانات المستخدمين
"""

import hashlib
from ..config.database_connection import db_connection
from ..models.user import User

class UserDAO:
    """فئة الوصول لبيانات المستخدمين"""
    
    def __init__(self):
        self.db = db_connection
    
    def _hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate(self, username, password):
        """التحقق من صحة بيانات تسجيل الدخول"""
        try:
            hashed_password = self._hash_password(password)
            query = """
            SELECT USER_ID, USERNAME, EMAIL, FULL_NAME, PHONE, USER_TYPE, STATUS
            FROM USERS 
            WHERE USERNAME = :1 AND PASSWORD = :2 AND STATUS = 'ACTIVE'
            """
            
            results = self.db.execute_query(query, (username, hashed_password))
            
            if results:
                row = results[0]
                user = User()
                user.user_id = row[0]
                user.username = row[1]
                user.email = row[2]
                user.full_name = row[3]
                user.phone = row[4]
                user.user_type = row[5]
                user.status = row[6]
                return user
            
            return None
            
        except Exception as e:
            print(f"خطأ في التحقق من المستخدم: {e}")
            # إرجاع مستخدم تجريبي في حالة عدم توفر قاعدة البيانات
            if username == "admin" and password == "admin123":
                user = User()
                user.user_id = 1
                user.username = "admin"
                user.full_name = "مدير النظام"
                user.email = "<EMAIL>"
                user.user_type = "ADMIN"
                user.status = "ACTIVE"
                return user
            elif username == "user1" and password == "user123":
                user = User()
                user.user_id = 2
                user.username = "user1"
                user.full_name = "مستخدم تجريبي"
                user.email = "<EMAIL>"
                user.user_type = "USER"
                user.status = "ACTIVE"
                return user
            
            return None

# -*- coding: utf-8 -*-
"""
User Model
نموذج المستخدم
"""

class User:
    """نموذج المستخدم"""
    
    def __init__(self):
        self.user_id = None
        self.username = None
        self.password = None
        self.email = None
        self.full_name = None
        self.phone = None
        self.user_type = 'USER'  # 'ADMIN', 'USER', 'MANAGER'
        self.status = 'ACTIVE'   # 'ACTIVE', 'INACTIVE'
        self.created_date = None
        self.last_login = None
    
    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        return self.user_type == 'ADMIN'
    
    def is_manager(self):
        """التحقق من كون المستخدم مدير قسم"""
        return self.user_type == 'MANAGER'
    
    def can_manage_users(self):
        """التحقق من صلاحية إدارة المستخدمين"""
        return self.user_type in ['ADMIN', 'MANAGER']
    
    def can_manage_products(self):
        """التحقق من صلاحية إدارة المنتجات"""
        return self.user_type in ['ADMIN', 'MANAGER']
